<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Admin Panel - Flori Construction</title>
    
    <!-- Performance Optimized CSS Loading -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Critical CSS - Bootstrap (minified) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Font Awesome (optimized loading) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous">

    <!-- DataTables CSS (minified) -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" crossorigin="anonymous">

    <!-- Google Fonts (optimized with font-display: swap) -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet" crossorigin="anonymous">
    <!-- Custom Admin CSS -->
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #5855eb;
            --secondary-color: #1e293b;
            --sidebar-bg: #0f172a;
            --sidebar-hover: #1e293b;
            --sidebar-width: 260px;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;

            /* Responsive breakpoints */
            --breakpoint-xs: 0;
            --breakpoint-sm: 576px;
            --breakpoint-md: 768px;
            --breakpoint-lg: 992px;
            --breakpoint-xl: 1200px;
            --breakpoint-xxl: 1400px;

            /* Fluid typography */
            --font-size-xs: clamp(0.7rem, 0.66rem + 0.2vw, 0.75rem);
            --font-size-sm: clamp(0.8rem, 0.75rem + 0.25vw, 0.875rem);
            --font-size-base: clamp(0.9rem, 0.85rem + 0.25vw, 1rem);
            --font-size-lg: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);
            --font-size-xl: clamp(1.1rem, 1rem + 0.5vw, 1.25rem);
            --font-size-2xl: clamp(1.3rem, 1.2rem + 0.5vw, 1.5rem);
            --font-size-3xl: clamp(1.5rem, 1.4rem + 0.5vw, 1.875rem);
            --font-size-4xl: clamp(1.8rem, 1.6rem + 1vw, 2.25rem);

            /* Fluid spacing */
            --spacing-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem);
            --spacing-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
            --spacing-md: clamp(1rem, 0.8rem + 1vw, 1.5rem);
            --spacing-lg: clamp(1.5rem, 1.2rem + 1.5vw, 2.25rem);
            --spacing-xl: clamp(2rem, 1.6rem + 2vw, 3rem);
            --spacing-xxl: clamp(3rem, 2.4rem + 3vw, 4.5rem);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 0.875rem;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--light-bg);
            font-weight: 400;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Plus Jakarta Sans', sans-serif;
            font-weight: 600;
            line-height: 1.4;
            color: var(--text-primary);
        }

        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 60px 0 0;
            box-shadow: var(--shadow-lg);
            background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0c1425 100%);
            width: var(--sidebar-width);
            border-right: 1px solid #1e293b;
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 60px);
            padding: 1.5rem 0;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            color: #cbd5e1;
            padding: 0.875rem 1.5rem;
            border-radius: 0;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
            margin: 0.125rem 0.75rem;
            border-radius: var(--radius-md);
            position: relative;
        }

        .sidebar .nav-link:hover {
            color: #ffffff;
            background-color: var(--sidebar-hover);
            transform: translateX(2px);
        }

        .sidebar .nav-link.active {
            color: #ffffff;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            box-shadow: var(--shadow-md);
        }

        .sidebar .nav-link.active::before {
            content: '';
            position: absolute;
            left: -0.75rem;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: var(--primary-color);
            border-radius: 0 2px 2px 0;
        }

        .sidebar .nav-link i {
            margin-right: 0.75rem;
            width: 18px;
            font-size: 1rem;
        }

        .sidebar-heading {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-muted);
            margin: 1.5rem 1.5rem 0.5rem;
        }

        .navbar-brand {
            padding: 1rem 1.5rem;
            background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0c1425 100%);
            box-shadow: var(--shadow-md);
            font-family: 'Plus Jakarta Sans', sans-serif;
            font-weight: 700;
            font-size: 1.125rem;
            border-right: 1px solid #1e293b;
        }

        .navbar .form-control {
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background-color: var(--card-bg);
            transition: all 0.2s ease-in-out;
        }

        .navbar .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .border-left-primary {
            border-left: 4px solid var(--primary-color) !important;
        }

        .border-left-success {
            border-left: 4px solid var(--success-color) !important;
        }

        .border-left-info {
            border-left: 4px solid var(--info-color) !important;
        }

        .border-left-warning {
            border-left: 4px solid var(--warning-color) !important;
        }

        .text-xs {
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .btn {
            font-weight: 500;
            border-radius: var(--radius-md);
            transition: all 0.2s ease-in-out;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            border: none;
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover) 0%, #4f46e5 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.8125rem;
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            transition: all 0.2s ease-in-out;
            margin-bottom: 1.5rem;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
        }

        .card-header {
            background-color: var(--light-bg);
            border-bottom: 1px solid var(--border-color);
            padding: 1.25rem 1.5rem;
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card.shadow {
            box-shadow: var(--shadow-md);
        }

        main {
            margin-left: var(--sidebar-width);
            padding: 2rem;
            min-height: 100vh;
        }

        .dropdown-toggle::after {
            margin-left: 0.5rem;
        }

        .message-item:last-child {
            border-bottom: none !important;
        }

        /* Stats Cards Styling */
        .stats-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            transition: all 0.2s ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stats-card .h5 {
            font-family: 'Plus Jakarta Sans', sans-serif;
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 0;
        }

        /* Table Styling */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background-color: var(--light-bg);
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            font-size: 0.8125rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            color: var(--text-secondary);
            padding: 1rem 1.25rem;
        }

        .table td {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: var(--light-bg);
        }

        /* Badge Styling */
        .badge {
            font-weight: 500;
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-sm);
        }

        .bg-success {
            background-color: var(--success-color) !important;
        }

        .bg-warning {
            background-color: var(--warning-color) !important;
        }

        .bg-info {
            background-color: var(--info-color) !important;
        }

        .bg-danger {
            background-color: var(--danger-color) !important;
        }

        /* Alert Styling */
        .alert {
            border: none;
            border-radius: var(--radius-md);
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background-color: #f0fdf4;
            color: #166534;
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background-color: #fef2f2;
            color: #991b1b;
            border-left: 4px solid var(--danger-color);
        }

        /* Navbar Styling */
        .navbar {
            background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0c1425 100%) !important;
            box-shadow: var(--shadow-md);
            padding: 0;
            height: 60px;
        }

        .dropdown-menu {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: var(--light-bg);
            color: var(--text-primary);
        }

        /* Page Header */
        .page-header {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .page-header h1 {
            margin-bottom: 0;
            font-size: 1.75rem;
            font-weight: 700;
        }

        /* Performance Optimizations */

        /* Image optimization and lazy loading */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        img[data-src] {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        img[data-src].loaded {
            opacity: 1;
        }

        /* Optimized image containers */
        .image-container {
            position: relative;
            overflow: hidden;
            border-radius: var(--radius-md);
        }

        .image-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: var(--radius-md);
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Optimized table rendering */
        .table-responsive {
            will-change: scroll-position;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--light-bg);
        }

        .table-responsive::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: var(--light-bg);
            border-radius: var(--radius-sm);
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: var(--primary-hover);
        }

        /* Efficient animations */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Reduce motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Enhanced Responsive Design */

        /* Extra small devices (portrait phones, less than 576px) */
        @media (max-width: 575.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding-top: 0;
            }

            main {
                margin-left: 0;
                padding: 0.75rem;
            }

            .navbar-brand {
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            .stats-card {
                margin-bottom: 0.75rem;
                padding: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .page-header {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .table th,
            .table td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
        }

        /* Small devices (landscape phones, 576px and up) */
        @media (min-width: 576px) and (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding-top: 0;
            }

            main {
                margin-left: 0;
                padding: 1rem;
            }

            .stats-card {
                margin-bottom: 1rem;
            }
        }

        /* Medium devices (tablets, 768px and up) */
        @media (min-width: 768px) and (max-width: 991.98px) {
            .sidebar {
                width: var(--sidebar-width);
                position: fixed;
                left: 0;
                top: 0;
                bottom: 0;
                padding-top: 60px;
            }

            main {
                margin-left: var(--sidebar-width);
                padding: 1.5rem;
            }
        }

        /* Large devices (desktops, 992px and up) */
        @media (min-width: 992px) {
            main {
                padding: 2rem;
            }

            .stats-card {
                padding: 2rem;
            }

            .card-body {
                padding: 2rem;
            }
        }

        /* Extra large devices (large desktops, 1200px and up) */
        @media (min-width: 1200px) {
            main {
                padding: 2.5rem;
            }

            .page-header {
                padding: 2rem 2.5rem;
            }
        }

        /* Responsive Utility Classes */

        /* Display utilities */
        .d-xs-none { display: none !important; }
        .d-xs-inline { display: inline !important; }
        .d-xs-inline-block { display: inline-block !important; }
        .d-xs-block { display: block !important; }
        .d-xs-flex { display: flex !important; }
        .d-xs-grid { display: grid !important; }

        @media (min-width: 576px) {
            .d-sm-none { display: none !important; }
            .d-sm-inline { display: inline !important; }
            .d-sm-inline-block { display: inline-block !important; }
            .d-sm-block { display: block !important; }
            .d-sm-flex { display: flex !important; }
            .d-sm-grid { display: grid !important; }
        }

        @media (min-width: 768px) {
            .d-md-none { display: none !important; }
            .d-md-inline { display: inline !important; }
            .d-md-inline-block { display: inline-block !important; }
            .d-md-block { display: block !important; }
            .d-md-flex { display: flex !important; }
            .d-md-grid { display: grid !important; }
        }

        @media (min-width: 992px) {
            .d-lg-none { display: none !important; }
            .d-lg-inline { display: inline !important; }
            .d-lg-inline-block { display: inline-block !important; }
            .d-lg-block { display: block !important; }
            .d-lg-flex { display: flex !important; }
            .d-lg-grid { display: grid !important; }
        }

        @media (min-width: 1200px) {
            .d-xl-none { display: none !important; }
            .d-xl-inline { display: inline !important; }
            .d-xl-inline-block { display: inline-block !important; }
            .d-xl-block { display: block !important; }
            .d-xl-flex { display: flex !important; }
            .d-xl-grid { display: grid !important; }
        }

        /* Text utilities */
        .text-xs-left { text-align: left !important; }
        .text-xs-center { text-align: center !important; }
        .text-xs-right { text-align: right !important; }

        @media (min-width: 576px) {
            .text-sm-left { text-align: left !important; }
            .text-sm-center { text-align: center !important; }
            .text-sm-right { text-align: right !important; }
        }

        @media (min-width: 768px) {
            .text-md-left { text-align: left !important; }
            .text-md-center { text-align: center !important; }
            .text-md-right { text-align: right !important; }
        }

        /* Spacing utilities */
        .p-responsive { padding: var(--spacing-md); }
        .m-responsive { margin: var(--spacing-md); }
        .gap-responsive { gap: var(--spacing-md); }

        /* Container utilities */
        .container-fluid-responsive {
            width: 100%;
            padding-right: var(--spacing-md);
            padding-left: var(--spacing-md);
            margin-right: auto;
            margin-left: auto;
        }

        @media (min-width: 576px) {
            .container-fluid-responsive {
                max-width: 540px;
                padding-right: var(--spacing-lg);
                padding-left: var(--spacing-lg);
            }
        }

        @media (min-width: 768px) {
            .container-fluid-responsive {
                max-width: 720px;
            }
        }

        @media (min-width: 992px) {
            .container-fluid-responsive {
                max-width: 960px;
            }
        }

        @media (min-width: 1200px) {
            .container-fluid-responsive {
                max-width: 1140px;
            }
        }

        /* Performance optimizations */
        .will-change-transform { will-change: transform; }
        .will-change-scroll { will-change: scroll-position; }
        .will-change-opacity { will-change: opacity; }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        @media (prefers-reduced-motion: reduce) {
            html {
                scroll-behavior: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3 fs-6 text-white" href="dashboard.php">
            <i class="fas fa-hard-hat"></i> Flori Construction Admin
        </a>
        
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <div class="dropdown">
                    <a class="nav-link px-3 dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                        <?php
                        // Get user's full name if available
                        $database = new Database();
                        $conn = $database->getConnection();
                        $stmt = $conn->prepare("SELECT first_name, last_name, avatar FROM users WHERE id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                        $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);

                        if ($userInfo && ($userInfo['first_name'] || $userInfo['last_name'])) {
                            $displayName = trim($userInfo['first_name'] . ' ' . $userInfo['last_name']);
                            echo htmlspecialchars($displayName);
                        } else {
                            echo $_SESSION['username'];
                        }
                        ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-cog"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar backdrop for mobile -->
    <div class="sidebar-backdrop" onclick="closeSidebar()"></div>
